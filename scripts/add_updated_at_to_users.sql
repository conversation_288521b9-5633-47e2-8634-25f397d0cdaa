-- Add updated_at column to users table
-- This script safely adds the updated_at column to the users table if it doesn't exist

-- Check if updated_at column exists and add it if missing
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'users'
   AND column_name = 'updated_at') = 0,
  'ALTER TABLE `users` ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "Last update timestamp" AFTER `created_at`',
  'SELECT "Column updated_at already exists in users table"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the column was added
SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN 'SUCCESS: updated_at column exists in users table'
    ELSE 'ERROR: updated_at column missing from users table'
  END as status
FROM INFORMATION_SCHEMA.COLUMNS
WHERE table_schema = DATABASE()
AND table_name = 'users'
AND column_name = 'updated_at';

-- Show the users table structure
DESCRIBE users;
