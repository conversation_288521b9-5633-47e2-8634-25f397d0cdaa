-- Password Reset Tokens Table
-- This table stores secure tokens for password reset functionality
-- Similar to email_verification_tokens but for password resets

CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL COMMENT 'Email address requesting password reset',
  `reset_token` varchar(255) NOT NULL COMMENT 'Cryptographically secure reset token',
  `expires_at` datetime NOT NULL COMMENT 'Token expiration timestamp (15-30 minutes)',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether token has been used',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Token creation timestamp',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
  <PERSON>IMARY KEY (`id`),
  UNIQUE KEY `unique_token` (`reset_token`),
  <PERSON><PERSON>Y `idx_email_expires_used` (`email`, `expires_at`, `is_used`),
  <PERSON><PERSON>Y `idx_token_expires` (`reset_token`, `expires_at`),
  KEY `idx_expires_cleanup` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens for secure password recovery';

-- Add indexes for performance
CREATE INDEX `idx_email_expires_used` 
  ON `password_reset_tokens` (`email`, `expires_at`, `is_used`);

CREATE INDEX `idx_token_expires` 
  ON `password_reset_tokens` (`reset_token`, `expires_at`);

-- Index for cleanup operations
CREATE INDEX `idx_expires_cleanup` 
  ON `password_reset_tokens` (`expires_at`);

-- Comments for documentation
ALTER TABLE `password_reset_tokens` COMMENT = 'Stores secure tokens for password reset functionality with expiration and usage tracking';
