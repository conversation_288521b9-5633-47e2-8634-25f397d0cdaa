-- Password Reset Tokens Table Migration
-- This script creates the password_reset_tokens table for secure password recovery
-- Handles existing indexes gracefully to avoid duplicate key errors

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL COMMENT 'Email address requesting password reset',
  `reset_token` varchar(255) NOT NULL COMMENT 'Cryptographically secure reset token',
  `expires_at` datetime NOT NULL COMMENT 'Token expiration timestamp (15-30 minutes)',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether token has been used',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Token creation timestamp',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens for secure password recovery';

-- Add indexes only if they don't exist (MySQL 5.7+ syntax)
-- For older MySQL versions, these will fail silently if indexes exist

-- Unique index for reset tokens
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_schema = DATABASE()
   AND table_name = 'password_reset_tokens'
   AND index_name = 'unique_token') = 0,
  'ALTER TABLE `password_reset_tokens` ADD UNIQUE KEY `unique_token` (`reset_token`)',
  'SELECT "Index unique_token already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Composite index for email, expires_at, is_used
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_schema = DATABASE()
   AND table_name = 'password_reset_tokens'
   AND index_name = 'idx_email_expires_used') = 0,
  'ALTER TABLE `password_reset_tokens` ADD KEY `idx_email_expires_used` (`email`, `expires_at`, `is_used`)',
  'SELECT "Index idx_email_expires_used already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Index for token and expires_at
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_schema = DATABASE()
   AND table_name = 'password_reset_tokens'
   AND index_name = 'idx_token_expires') = 0,
  'ALTER TABLE `password_reset_tokens` ADD KEY `idx_token_expires` (`reset_token`, `expires_at`)',
  'SELECT "Index idx_token_expires already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Index for cleanup operations
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_schema = DATABASE()
   AND table_name = 'password_reset_tokens'
   AND index_name = 'idx_expires_cleanup') = 0,
  'ALTER TABLE `password_reset_tokens` ADD KEY `idx_expires_cleanup` (`expires_at`)',
  'SELECT "Index idx_expires_cleanup already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update table comment
ALTER TABLE `password_reset_tokens` COMMENT = 'Stores secure tokens for password reset functionality with expiration and usage tracking';

-- Show final table structure
SHOW CREATE TABLE `password_reset_tokens`;
