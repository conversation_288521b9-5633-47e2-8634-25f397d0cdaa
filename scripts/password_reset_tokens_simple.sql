-- Simple Password Reset Tokens Table Migration
-- Alternative script that drops and recreates the table (use with caution in production)

-- Drop table if it exists (WARNING: This will delete all existing data)
DROP TABLE IF EXISTS `password_reset_tokens`;

-- Create the table with all indexes
CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL COMMENT 'Email address requesting password reset',
  `reset_token` varchar(255) NOT NULL COMMENT 'Cryptographically secure reset token',
  `expires_at` datetime NOT NULL COMMENT 'Token expiration timestamp (15-30 minutes)',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether token has been used',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Token creation timestamp',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
  <PERSON><PERSON><PERSON>Y KEY (`id`),
  <PERSON>IQUE KEY `unique_token` (`reset_token`),
  <PERSON><PERSON>Y `idx_email_expires_used` (`email`, `expires_at`, `is_used`),
  KEY `idx_token_expires` (`reset_token`, `expires_at`),
  KEY `idx_expires_cleanup` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens for secure password recovery';

-- Show the created table
DESCRIBE `password_reset_tokens`;
