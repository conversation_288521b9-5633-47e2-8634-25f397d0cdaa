"""
Connection Pool Monitoring for Aurora MySQL
Tracks connection usage to prevent exhaustion
"""

import asyncio
import logging
import threading
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class ConnectionStats:
    """Connection statistics snapshot"""
    timestamp: float
    total_pools: int
    total_connections: int
    max_connections_limit: int
    utilization_percent: float
    active_event_loops: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ConnectionMonitor:
    """
    Monitors MySQL connection pool usage across all event loops
    Provides alerts when approaching Aurora's connection limits
    """
    
    def __init__(self, max_connections: int = 60, alert_threshold: float = 0.8):
        self.max_connections = max_connections
        self.alert_threshold = alert_threshold
        self._stats_history = []
        self._lock = threading.Lock()
        self._last_alert_time = 0
        self._alert_cooldown = 300  # 5 minutes between alerts
        
    def get_current_stats(self) -> ConnectionStats:
        """Get current connection statistics"""
        from .mysql_mgr import _shared_mysql_pools
        
        with self._lock:
            total_pools = len(_shared_mysql_pools)
            total_connections = 0
            active_loops = 0
            
            for loop_id, pool in _shared_mysql_pools.items():
                if not pool.closed:
                    active_loops += 1
                    # Estimate current connections (size might not be exact)
                    total_connections += pool.size
            
            utilization = (total_connections / self.max_connections) * 100 if self.max_connections > 0 else 0
            
            stats = ConnectionStats(
                timestamp=time.time(),
                total_pools=total_pools,
                total_connections=total_connections,
                max_connections_limit=self.max_connections,
                utilization_percent=utilization,
                active_event_loops=active_loops
            )
            
            # Store in history (keep last 100 entries)
            self._stats_history.append(stats)
            if len(self._stats_history) > 100:
                self._stats_history.pop(0)
            
            # Check for alerts
            self._check_alert_conditions(stats)
            
            return stats
    
    def _check_alert_conditions(self, stats: ConnectionStats):
        """Check if we should send alerts"""
        current_time = time.time()
        
        # Only alert once per cooldown period
        if current_time - self._last_alert_time < self._alert_cooldown:
            return
            
        if stats.utilization_percent >= (self.alert_threshold * 100):
            logger.warning(
                f"HIGH CONNECTION USAGE ALERT: {stats.utilization_percent:.1f}% "
                f"({stats.total_connections}/{stats.max_connections_limit} connections) "
                f"across {stats.total_pools} pools"
            )
            self._last_alert_time = current_time
    
    def get_stats_summary(self, minutes: int = 10) -> Dict[str, Any]:
        """Get statistics summary for the last N minutes"""
        cutoff_time = time.time() - (minutes * 60)
        
        with self._lock:
            recent_stats = [s for s in self._stats_history if s.timestamp >= cutoff_time]
            
            if not recent_stats:
                return {"error": f"No stats available for last {minutes} minutes"}
            
            max_utilization = max(s.utilization_percent for s in recent_stats)
            avg_utilization = sum(s.utilization_percent for s in recent_stats) / len(recent_stats)
            max_connections = max(s.total_connections for s in recent_stats)
            
            return {
                "period_minutes": minutes,
                "samples": len(recent_stats),
                "max_utilization_percent": max_utilization,
                "avg_utilization_percent": avg_utilization,
                "max_connections_used": max_connections,
                "connection_limit": self.max_connections,
                "alert_threshold_percent": self.alert_threshold * 100,
                "latest_stats": recent_stats[-1].to_dict() if recent_stats else None
            }
    
    def force_cleanup_if_needed(self) -> Dict[str, Any]:
        """Force cleanup of closed pools if utilization is too high"""
        stats = self.get_current_stats()
        
        if stats.utilization_percent >= 95:  # Emergency threshold
            logger.warning("EMERGENCY: Forcing connection pool cleanup due to high utilization")
            
            from .mysql_mgr import _shared_mysql_pools
            cleaned_pools = 0
            
            with self._lock:
                pools_to_remove = []
                for loop_id, pool in _shared_mysql_pools.items():
                    if pool.closed:
                        pools_to_remove.append(loop_id)
                
                for loop_id in pools_to_remove:
                    del _shared_mysql_pools[loop_id]
                    cleaned_pools += 1
            
            logger.info(f"Emergency cleanup removed {cleaned_pools} closed pools")
            
            return {
                "cleanup_performed": True,
                "pools_cleaned": cleaned_pools,
                "stats_before": stats.to_dict(),
                "stats_after": self.get_current_stats().to_dict()
            }
        
        return {"cleanup_performed": False, "reason": "Utilization below emergency threshold"}

# Global monitor instance
_connection_monitor: Optional[ConnectionMonitor] = None
_monitor_lock = threading.Lock()

def get_connection_monitor(max_connections: int = 60) -> ConnectionMonitor:
    """Get or create the global connection monitor"""
    global _connection_monitor
    
    with _monitor_lock:
        if _connection_monitor is None:
            _connection_monitor = ConnectionMonitor(max_connections=max_connections)
        return _connection_monitor

def log_connection_stats():
    """Log current connection statistics (for debugging)"""
    monitor = get_connection_monitor()
    stats = monitor.get_current_stats()
    
    logger.info(
        f"Connection Stats: {stats.total_connections}/{stats.max_connections_limit} "
        f"({stats.utilization_percent:.1f}%) across {stats.total_pools} pools"
    )
    
    return stats.to_dict()
