from .helper import fetch_row, insert, update_row, delete
from ...models import sql_scripts

__all__ = [
    'store_password_reset_token', 'get_password_reset_token', 'mark_password_reset_token_used',
    'get_recent_password_reset_token', 'cleanup_expired_password_reset_tokens',
    'get_password_reset_attempts_count', 'invalidate_all_password_reset_tokens_for_email',
    'get_user_by_email_for_password_reset', 'update_user_password_for_reset'
]


async def store_password_reset_token(conn_pool, email, reset_token, expires_at):
    """
    Store password reset token in the database
    Uses INSERT ... ON DUPLICATE KEY UPDATE to replace existing token for the same email
    """
    return await insert(
        conn_pool,
        sql_stmt=sql_scripts['store_password_reset_token'],
        params={
            "email": email,
            "reset_token": reset_token,
            "expires_at": expires_at
        }
    )


async def get_password_reset_token(conn_pool, token):
    """
    Retrieve password reset token information by token
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_password_reset_token'],
        params={"reset_token": token}
    )


async def get_recent_password_reset_token(conn_pool, email, cooldown_minutes):
    """
    Get recent reset token for cooldown check
    """
    from datetime import datetime, timedelta
    cooldown_start = datetime.now() - timedelta(minutes=cooldown_minutes)

    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_recent_password_reset_token'],
        params={"email": email, "cooldown_start": cooldown_start}
    )


async def mark_password_reset_token_used(conn_pool, token):
    """
    Mark a reset token as used
    """
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts['mark_password_reset_token_used'],
        params={"reset_token": token}
    )


async def cleanup_expired_password_reset_tokens(conn_pool):
    """
    Clean up expired password reset tokens
    """
    from datetime import datetime
    return await delete(
        conn_pool,
        sql_stmt=sql_scripts['cleanup_expired_password_reset_tokens'],
        params={"current_time": datetime.now()}
    )


async def get_password_reset_attempts_count(conn_pool, email, hours):
    """
    Get number of reset attempts for an email in the last N hours
    """
    from datetime import datetime, timedelta
    since_time = datetime.now() - timedelta(hours=hours)

    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_password_reset_attempts_count'],
        params={"email": email, "since_time": since_time}
    )
    return result[0] if result else 0


async def invalidate_all_password_reset_tokens_for_email(conn_pool, email):
    """
    Invalidate all reset tokens for an email (after successful password reset)
    """
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts['invalidate_all_password_reset_tokens_for_email'],
        params={"email": email}
    )


async def get_user_by_email_for_password_reset(conn_pool, email):
    """
    Check if user exists with given email
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_user_by_email_for_password_reset'],
        params={"email": email}
    )


async def update_user_password_for_reset(conn_pool, email, password_hash):
    """
    Update user's password
    """
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts['update_user_password_for_reset'],
        params={"password_hash": password_hash, "email": email}
    )
