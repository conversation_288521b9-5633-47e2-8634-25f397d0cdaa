import aiomysql
import asyncio
import threading
import logging

from app import app

logger = logging.getLogger(__name__)

__all__ = ["mysql_connection_pool_factory", "get_shared_mysql_pool", "cleanup_mysql_pool_for_current_loop", "get_connection_stats"]

# Global shared connection pool for Celery tasks - keyed by event loop
_shared_mysql_pools = {}
_pool_lock = threading.Lock()


async def mysql_connection_pool_factory(**kwargs):
    """ This method is used to create MySQL pool creation """

    minsize = kwargs.get('minsize', app.config.MYSQL_CONNECTION_POOL_MINIMUM_SIZE)
    maxsize = kwargs.get('maxsize', app.config.MYSQL_CONNECTION_POOL_MAXIMUM_SIZE)
    host = kwargs.get('host', app.config.MYSQL_DATABASE_HOST)
    user = kwargs.get('user', app.config.MYSQL_DATABASE_USER)
    password = kwargs.get('password', app.config.MYSQL_DATABASE_PASSWORD)
    db = kwargs.get('db', app.config.MYSQL_DATABASE_NAME)
    pool_recycle = kwargs.get('pool_recycle', app.config.MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME)

    return await aiomysql.create_pool(
        minsize=minsize,
        maxsize=maxsize,
        host=host,
        user=user,
        password=password,
        db=db,
        pool_recycle=pool_recycle,
        cursorclass=aiomysql.DictCursor,
        autocommit=True,
    )


async def get_shared_mysql_pool():
    """
    Get or create a shared MySQL connection pool for Celery tasks.
    This prevents each task from creating its own pool and exhausting connections.
    Each event loop gets its own pool to avoid "attached to a different loop" errors.
    """
    global _shared_mysql_pools

    # Get the current event loop
    try:
        current_loop = asyncio.get_running_loop()
        loop_id = id(current_loop)
    except RuntimeError:
        # No running loop, create a new one
        current_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(current_loop)
        loop_id = id(current_loop)

    with _pool_lock:
        # Check if we have a pool for this event loop
        if loop_id not in _shared_mysql_pools or _shared_mysql_pools[loop_id].closed:
            # Create a larger shared pool for concurrent Celery tasks
            # Increase pool size to handle more concurrent tasks
            try:
                _shared_mysql_pools[loop_id] = await mysql_connection_pool_factory(
                    minsize=1,  # Conservative minimum for Aurora
                    maxsize=8,  # Conservative maximum to stay within 60 connection limit
                )
                logger.info(f"Created new MySQL pool for event loop {loop_id} (total pools: {len(_shared_mysql_pools)})")
            except Exception as e:
                # If pool creation fails, remove the entry and re-raise
                if loop_id in _shared_mysql_pools:
                    del _shared_mysql_pools[loop_id]
                logger.error(f"Failed to create MySQL pool for event loop {loop_id}: {e}")
                raise e

    # Log connection stats periodically
    try:
        from .connection_monitor import log_connection_stats
        log_connection_stats()
    except Exception:
        pass  # Don't fail if monitoring fails

    return _shared_mysql_pools[loop_id]


async def cleanup_mysql_pool_for_current_loop():
    """
    Clean up the MySQL connection pool for the current event loop.
    Should be called when the event loop is about to be closed.
    """
    global _shared_mysql_pools

    try:
        current_loop = asyncio.get_running_loop()
        loop_id = id(current_loop)

        with _pool_lock:
            if loop_id in _shared_mysql_pools:
                pool = _shared_mysql_pools[loop_id]
                if not pool.closed:
                    pool.close()
                    await pool.wait_closed()
                del _shared_mysql_pools[loop_id]
                logger.info(f"Cleaned up MySQL pool for event loop {loop_id}")
    except RuntimeError:
        # No running loop, nothing to clean up
        pass


def get_connection_stats():
    """
    Get current connection pool statistics for monitoring
    """
    global _shared_mysql_pools

    with _pool_lock:
        total_pools = len(_shared_mysql_pools)
        total_connections = 0
        active_pools = 0

        for loop_id, pool in _shared_mysql_pools.items():
            if not pool.closed:
                active_pools += 1
                total_connections += pool.size

        return {
            "total_pools": total_pools,
            "active_pools": active_pools,
            "total_connections": total_connections,
            "max_connections_per_pool": 8,  # From our configuration
            "min_connections_per_pool": 1,  # From our configuration
            "aurora_connection_limit": 60,  # Aurora db.t4g.micro limit
            "utilization_percent": (total_connections / 60) * 100 if total_connections > 0 else 0
        }
