import asyncio
import aio_pika
from aio_pika.exceptions import ChannelPreconditionFailed, ChannelNotFoundEntity

from app.common import QueueEnum
from app.core.services.queue_service.consumer import ConsumerScanStatusUpdates<PERSON><PERSON><PERSON>, ConsumerScanDLQHandler
from app.core.services.queue_service.common import get_queue_consumers_count

__all__ = ['get_connection', 'get_channel', 'rmq_pool_factory', 'load_consumers', 'queue_init',
           'restart_dead_consumers']

consumer_map = {
    QueueEnum.SCAN_STATUS_UPDATES.name: ConsumerScanStatusUpdatesHandler,
    QueueEnum.SCAN_STATUS_UPDATES_FAILED.name: ConsumerScanDLQHandler,
}


async def get_connection(app):
    """ This method is used to create RabbitMQ connection """
    url = f'amqp://{app.config.RABBIT_MQ_USERNAME}:{app.config.RABBIT_MQ_PASSWORD}@' \
          f'{app.config.RABBIT_MQ_HOST}:{app.config.RABBIT_MQ_PORT}/' \
          f'{app.config.RABBIT_MQ_V_HOST}?heartbeat={app.config.RABBIT_MQ_HEARTBEAT}' \
          '&retry_delay=3&connection_attempts=3&blocked_connection_timeout=300'

    return await aio_pika.connect_robust(url=url)


async def get_channel(app) -> aio_pika.Channel:
    """ This method is used to create a channel """

    async with app.rmq_connection_pool.acquire() as connection:
        return await connection.channel()


async def restart_dead_consumers():
    loop = asyncio.get_running_loop()
    for data in QueueEnum:
        if data.value['worker']:
            active_consumer = await get_queue_consumers_count(data.value['queue_name'])
            dead_workers = data.value['worker'] - active_consumer
            if dead_workers > 0:
                await load_consumers(loop, data.name, data.value['queue_name'], dead_workers)


async def rmq_pool_factory(app):
    """ This method is used to create a pool of channel """

    app.rmq_connection_pool = aio_pika.pool.Pool(get_connection,
                                                 app,
                                                 max_size=app.config.RABBITMQ_CONNECTION_POOL_MAX_SIZE)
    channel_pool = aio_pika.pool.Pool(get_channel,
                                      app,
                                      max_size=app.config.RABBITMQ_CHANNEL_POOL_MAX_SIZE)
    return channel_pool


async def load_consumers(loop, consumer, queue_name, consumer_count):
    """ This method is used to add consumers for the requested queue with requested consumer count

    :param loop :-  Async Event loop
    :param consumer:- Consumer name
    :param queue_name:- RabbitMQ queue name
    :param  consumer_count:- Consumer count
    """

    for _ in range(consumer_count):
        service = consumer_map[consumer](queue_name=queue_name)
        loop.create_task(service.run())


async def _declare_exchange_and_queues(app, channel: aio_pika.Channel):
    """Declare exchanges and queues with DLQ configuration, self-healing if arguments differ."""
    for data in QueueEnum:
        # Declare exchange (idempotent)
        exchange = await channel.declare_exchange(
            name=data.value["exchange_name"],
            type=data.value["exchange_type"],
            durable=True,
        )

        # Handle different queue types
        if data.name == QueueEnum.SCAN_STATUS_UPDATES.name:
            # Main queue: enforce DLX arguments
            desired_args = {
                'x-dead-letter-exchange': data.value["exchange_name"],
                'x-dead-letter-routing-key': QueueEnum.SCAN_STATUS_UPDATES_FAILED.value["route_key"],
            }
            try:
                # Try a passive declare first to detect mismatches without closing channel
                await channel.declare_queue(name=data.value["queue_name"], passive=True)
                # If passive declare succeeded, attempt to declare with desired args (no-op if matches)
                queue = await channel.declare_queue(name=data.value["queue_name"], durable=True, arguments=desired_args)
            except (ChannelPreconditionFailed, ChannelNotFoundEntity) as e:
                if isinstance(e, ChannelNotFoundEntity):
                    # Queue doesn't exist, create it on a fresh channel to avoid RPC timeout
                    async with app.rmq_channel_pool.acquire() as fresh_channel:
                        queue = await fresh_channel.declare_queue(name=data.value["queue_name"], durable=True, arguments=desired_args)
                        await queue.bind(exchange=exchange, routing_key=data.value["route_key"])
                        app.logger.info(f"Created new queue with DLX args: {data.value['queue_name']}")
                        continue
                else:
                    # Queue exists with different arguments; recreate on a fresh channel to avoid RPC timeout
                    async with app.rmq_channel_pool.acquire() as fresh_channel:
                        try:
                            await fresh_channel.queue_delete(data.value["queue_name"])  # drops messages in that queue
                        except Exception:
                            # If delete still fails, continue to create with a suffix to avoid conflict
                            suffixed_name = f"{data.value['queue_name']}-v2"
                            queue = await fresh_channel.declare_queue(name=suffixed_name, durable=True, arguments=desired_args)
                            await queue.bind(exchange=exchange, routing_key=data.value["route_key"]) 
                            continue
                        queue = await fresh_channel.declare_queue(name=data.value["queue_name"], durable=True, arguments=desired_args)
            await queue.bind(exchange=exchange, routing_key=data.value["route_key"])
            
        elif data.name in [QueueEnum.SCAN_STATUS_UPDATES_FAILED.name, QueueEnum.SCAN_STATUS_UPDATES_DLQ.name, QueueEnum.SCAN_STATUS_UPDATES_RETRY.name]:
            # DLQ, Failed, and Retry queues: simple creation
            try:
                # Try passive declare first
                await channel.declare_queue(name=data.value["queue_name"], passive=True)
                # If exists, declare with same args (idempotent)
                queue = await channel.declare_queue(name=data.value["queue_name"], durable=True)
            except ChannelNotFoundEntity:
                # Queue doesn't exist, create it
                async with app.rmq_channel_pool.acquire() as fresh_channel:
                    queue = await fresh_channel.declare_queue(name=data.value["queue_name"], durable=True)
                    await queue.bind(exchange=exchange, routing_key=data.value["route_key"])
                    app.logger.info(f"Created new queue: {data.value['queue_name']}")
                    continue
            await queue.bind(exchange=exchange, routing_key=data.value["route_key"])
            
        else:
            # Fallback for any other queue types
            try:
                await channel.declare_queue(name=data.value["queue_name"], passive=True)
                queue = await channel.declare_queue(name=data.value["queue_name"], durable=True)
            except ChannelNotFoundEntity:
                async with app.rmq_channel_pool.acquire() as fresh_channel:
                    queue = await fresh_channel.declare_queue(name=data.value["queue_name"], durable=True)
                    await queue.bind(exchange=exchange, routing_key=data.value["route_key"])
                    app.logger.info(f"Created new queue: {data.value['queue_name']}")
                    continue
            await queue.bind(exchange=exchange, routing_key=data.value["route_key"])


async def queue_init(app):
    """Start RabbitMQ operations on application start."""
    loop = asyncio.get_running_loop()
    async with app.rmq_channel_pool.acquire() as channel:
        await _declare_exchange_and_queues(app, channel)

    # Load Consumers
    for data in QueueEnum:
        if data.name in consumer_map and data.value['worker']:
            await load_consumers(loop, data.name, data.value['queue_name'], data.value['worker'])
