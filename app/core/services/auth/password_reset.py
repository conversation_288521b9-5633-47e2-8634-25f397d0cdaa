"""
Password Reset Authentication Services

This module contains authentication services for password reset functionality.
Similar to email_verification.py but for password reset operations.
"""

import hashlib
from typing import Dict, Any
from app import app
from app.core.services.password_reset_service import PasswordResetService
from app.core.services.celery_conf.tasks import send_password_reset_email_task
from app.core.models.mysql import (
    get_user_by_email_for_password_reset,
    update_user_password_for_reset,
    invalidate_all_password_reset_tokens_for_email,
    get_shared_mysql_pool
)
from app.common import (
    CloudAuditException, OTPExpiredException, OTPNotFoundException,
    OTPAlreadyVerifiedException, OTPResendCooldownException, get_encrypted_password
)

__all__ = [
    'ForgotPasswordService', 
    'ResetPasswordService', 
    'ValidateResetTokenService'
]


class ForgotPasswordService:
    """Service for handling forgot password requests"""
    
    def __init__(self, message):
        self.message = message
        self.password_reset_service = PasswordResetService()
    
    async def process(self) -> Dict[str, Any]:
        """
        Process forgot password request
        
        Returns:
            Dict containing result information
            
        Raises:
            OTPResendCooldownException: If rate limited
            CloudAuditException: If processing fails
        """
        try:
            email = self.message.email.lower().strip()
            
            # Check if user exists (but don't reveal if they don't for security)
            conn_pool = await get_shared_mysql_pool()
            user = await get_user_by_email_for_password_reset(conn_pool, email)
            
            # Always return success to prevent email enumeration attacks
            # But only send email if user actually exists
            if user:
                # Rate limiting removed for development/testing - can be re-enabled in production
                # if await self.password_reset_service.is_rate_limited(
                #     email,
                #     app.config.PASSWORD_RESET_MAX_ATTEMPTS,
                #     1  # 1 hour window
                # ):
                #     raise OTPResendCooldownException(
                #         "Too many password reset attempts. Please try again later."
                #     )
                
                # Create reset token
                token_info = await self.password_reset_service.create_reset_token(email)
                
                # Send password reset email via Celery task
                send_password_reset_email_task.delay(
                    recipient_email=email,
                    reset_url=token_info['reset_url'],
                    first_name=user.get('first_name')
                )
            
            # Always return the same response for security
            return {
                "message": "If an account with that email exists, we've sent a password reset link.",
                "email": email,
                "expires_in_minutes": app.config.PASSWORD_RESET_EXPIRY_MINUTES
            }
            
        except OTPResendCooldownException:
            raise
        except Exception as e:
            raise CloudAuditException(f"Failed to process forgot password request: {str(e)}")


class ValidateResetTokenService:
    """Service for validating password reset tokens"""
    
    def __init__(self, message):
        self.message = message
        self.password_reset_service = PasswordResetService()
    
    async def process(self) -> Dict[str, Any]:
        """
        Validate password reset token
        
        Returns:
            Dict containing token validation info
            
        Raises:
            OTPNotFoundException: If token doesn't exist
            OTPExpiredException: If token has expired
            OTPAlreadyVerifiedException: If token has been used
        """
        try:
            token = self.message.token
            
            # Validate the token
            token_info = await self.password_reset_service.validate_reset_token(token)
            
            return {
                "valid": True,
                "email": token_info['email'],
                "token": token,
                "expires_at": token_info['expires_at']
            }
            
        except (OTPNotFoundException, OTPExpiredException, OTPAlreadyVerifiedException):
            raise
        except Exception as e:
            raise CloudAuditException(f"Failed to validate reset token: {str(e)}")


class ResetPasswordService:
    """Service for handling password reset with new password"""
    
    def __init__(self, message):
        self.message = message
        self.password_reset_service = PasswordResetService()
    
    async def process(self) -> Dict[str, Any]:
        """
        Process password reset with new password
        
        Returns:
            Dict containing result information
            
        Raises:
            OTPNotFoundException: If token doesn't exist
            OTPExpiredException: If token has expired
            OTPAlreadyVerifiedException: If token has been used
            CloudAuditException: If password update fails
        """
        try:
            token = self.message.token
            new_password = self.message.new_password
            confirm_password = self.message.confirm_password
            
            # Validate passwords match
            if new_password != confirm_password:
                raise CloudAuditException("Passwords do not match.")
            
            # Validate password strength (basic validation)
            self._validate_password_strength(new_password)
            
            # Validate and use the reset token
            token_info = await self.password_reset_service.use_reset_token(token)
            email = token_info['email']
            
            # Hash the new password using the same method as login
            password_hash = get_encrypted_password(new_password)
            
            # Update user's password
            conn_pool = await get_shared_mysql_pool()
            result = await update_user_password_for_reset(conn_pool, email, password_hash)
            if not result or result.get("row_count", 0) == 0:
                raise CloudAuditException("Failed to update password. User may not exist.")

            # Invalidate all other reset tokens for this email
            await invalidate_all_password_reset_tokens_for_email(conn_pool, email)
            
            return {
                "message": "Password reset successfully.",
                "email": email,
                "success": True
            }
            
        except (OTPNotFoundException, OTPExpiredException, OTPAlreadyVerifiedException):
            raise
        except CloudAuditException:
            raise
        except Exception as e:
            raise CloudAuditException(f"Failed to reset password: {str(e)}")
    
    def _validate_password_strength(self, password: str) -> None:
        """
        Validate password strength requirements
        
        Args:
            password: Password to validate
            
        Raises:
            CloudAuditException: If password doesn't meet requirements
        """
        if len(password) < 8:
            raise CloudAuditException("Password must be at least 8 characters long.")
        
        if not any(c.isupper() for c in password):
            raise CloudAuditException("Password must contain at least one uppercase letter.")
        
        if not any(c.islower() for c in password):
            raise CloudAuditException("Password must contain at least one lowercase letter.")
        
        if not any(c.isdigit() for c in password):
            raise CloudAuditException("Password must contain at least one number.")
        
        # Check for special characters
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            raise CloudAuditException("Password must contain at least one special character.")
    



class ResendPasswordResetService:
    """Service for resending password reset links"""
    
    def __init__(self, message):
        self.message = message
        self.forgot_password_service = ForgotPasswordService(message)
    
    async def process(self) -> Dict[str, Any]:
        """
        Process resend password reset request
        
        Returns:
            Dict containing result information
        """
        # Reuse the forgot password service logic
        result = await self.forgot_password_service.process()
        
        # Update message for resend context
        result["message"] = "If an account with that email exists, we've sent a new password reset link."
        
        return result
