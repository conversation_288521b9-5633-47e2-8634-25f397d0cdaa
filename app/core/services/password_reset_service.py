"""
Password Reset Service

This service handles password reset token generation, validation, and management.
Similar to EmailVerificationService but specifically for password resets.
"""

import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from app import app
from app.core.models.mysql import (
    store_password_reset_token,
    get_password_reset_token,
    mark_password_reset_token_used,
    get_recent_password_reset_token,
    cleanup_expired_password_reset_tokens,
    get_password_reset_attempts_count,
    invalidate_all_password_reset_tokens_for_email,
    get_user_by_email_for_password_reset,
    update_user_password_for_reset,
    get_shared_mysql_pool
)
from app.common import (
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
    OTPResendCooldownException
)

__all__ = ['PasswordResetService']


class PasswordResetService:
    """Service for managing password reset tokens"""

    def __init__(self):
        self.expiry_minutes = app.config.PASSWORD_RESET_EXPIRY_MINUTES
        self.cooldown_minutes = app.config.PASSWORD_RESET_COOLDOWN_MINUTES
    
    def generate_reset_token(self) -> str:
        """
        Generate a cryptographically secure password reset token
        
        Returns:
            str: 64+ character secure token
        """
        return secrets.token_urlsafe(64)
    
    def generate_reset_url(self, token: str) -> str:
        """
        Generate complete password reset URL
        
        Args:
            token: Reset token
            
        Returns:
            str: Complete reset URL
        """
        base_url = app.config.BASE_URL
        return f"{base_url}/api/reset-password?token={token}"
    
    async def create_reset_token(self, email: str) -> Dict[str, Any]:
        """
        Create a new password reset token for the given email

        Args:
            email: User's email address

        Returns:
            Dict containing token info

        Raises:
            OTPResendCooldownException: If trying to resend too quickly
        """
        conn_pool = await get_shared_mysql_pool()

        # Check for existing recent token (cooldown)
        existing_token = await get_recent_password_reset_token(conn_pool, email, self.cooldown_minutes)
        if existing_token:
            remaining_time = self._calculate_remaining_cooldown(existing_token['created_at'])
            raise OTPResendCooldownException(
                f"Please wait {remaining_time} seconds before requesting another reset link."
            )
        
        # Generate new token
        reset_token = self.generate_reset_token()
        expires_at = datetime.now() + timedelta(minutes=self.expiry_minutes)
        
        # Store token in database
        await store_password_reset_token(
            conn_pool,
            email=email,
            reset_token=reset_token,
            expires_at=expires_at
        )
        
        return {
            "email": email,
            "reset_token": reset_token,
            "reset_url": self.generate_reset_url(reset_token),
            "expires_at": expires_at,
            "expires_in_minutes": self.expiry_minutes
        }
    
    async def validate_reset_token(self, token: str) -> Dict[str, Any]:
        """
        Validate a password reset token
        
        Args:
            token: Reset token to validate
            
        Returns:
            Dict containing token info
            
        Raises:
            OTPNotFoundException: If token doesn't exist
            OTPExpiredException: If token has expired
            OTPAlreadyVerifiedException: If token has been used
        """
        conn_pool = await get_shared_mysql_pool()

        # Get token from database
        token_data = await get_password_reset_token(conn_pool, token)
        if not token_data:
            raise OTPNotFoundException("Invalid or expired reset token.")
        
        # Check if token has been used
        if token_data['is_used']:
            raise OTPAlreadyVerifiedException("This reset link has already been used.")
        
        # Check if token has expired
        if datetime.now() > token_data['expires_at']:
            raise OTPExpiredException("This reset link has expired. Please request a new one.")
        
        return {
            "email": token_data['email'],
            "token": token,
            "expires_at": token_data['expires_at']
        }
    
    async def use_reset_token(self, token: str) -> Dict[str, Any]:
        """
        Mark a reset token as used
        
        Args:
            token: Reset token to mark as used
            
        Returns:
            Dict containing token info
        """
        # Validate token first
        token_info = await self.validate_reset_token(token)
        
        # Mark token as used
        conn_pool = await get_shared_mysql_pool()
        await mark_password_reset_token_used(conn_pool, token)
        
        return token_info
    
    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired password reset tokens
        
        Returns:
            int: Number of tokens cleaned up
        """
        conn_pool = await get_shared_mysql_pool()
        return await cleanup_expired_password_reset_tokens(conn_pool)
    
    def _calculate_remaining_cooldown(self, created_at: datetime) -> int:
        """
        Calculate remaining cooldown time in seconds
        
        Args:
            created_at: Token creation timestamp
            
        Returns:
            int: Remaining seconds
        """
        cooldown_end = created_at + timedelta(minutes=self.cooldown_minutes)
        remaining = cooldown_end - datetime.now()
        return max(0, int(remaining.total_seconds()))
    
    async def get_reset_attempts_count(self, email: str, hours: int = 1) -> int:
        """
        Get number of reset attempts for an email in the last N hours
        
        Args:
            email: Email address
            hours: Number of hours to look back
            
        Returns:
            int: Number of attempts
        """
        conn_pool = await get_shared_mysql_pool()
        return await get_password_reset_attempts_count(conn_pool, email, hours)
    
    async def is_rate_limited(self, email: str, max_attempts: int = 3, hours: int = 1) -> bool:
        """
        Check if email is rate limited for password resets
        
        Args:
            email: Email address
            max_attempts: Maximum attempts allowed
            hours: Time window in hours
            
        Returns:
            bool: True if rate limited
        """
        attempts = await self.get_reset_attempts_count(email, hours)
        return attempts >= max_attempts
