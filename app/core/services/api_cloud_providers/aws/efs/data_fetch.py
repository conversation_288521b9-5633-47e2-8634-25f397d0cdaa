import asyncio
import json
import os
import logging
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

# Set up logging
logger = logging.getLogger(__name__)

CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "efs")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"efs_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "efs")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of EFS-related resources that checks can consume.
    Following EC2's simple pattern to avoid deadlocks - FAST execution only.
    """
    # Region validation already done in fetch_and_cache_efs_region_data
    # Keep this function simple and fast like EC2

    try:
        async with session.client(AWSServiceNameEnum.EFS.value, region_name=region) as client:
            # Basic EFS API calls - keep it simple like EC2 to avoid deadlocks
            file_systems = await client.describe_file_systems()

            # Collect additional data needed for compliance checks
            access_points = {}
            mount_targets = {}
            backup_policies = {}
            file_system_policies = {}

            # For each file system, collect related resources
            for fs in file_systems.get("FileSystems", []):
                fs_id = fs.get("FileSystemId")
                if not fs_id:
                    continue

                try:
                    # Get access points for this file system
                    access_points[fs_id] = await client.describe_access_points(FileSystemId=fs_id)
                except Exception as e:
                    logger.warning(f"Error fetching access points for EFS {fs_id} in {region}: {e}")
                    access_points[fs_id] = {"AccessPoints": []}

                try:
                    # Get mount targets for this file system
                    mount_targets[fs_id] = await client.describe_mount_targets(FileSystemId=fs_id)
                except Exception as e:
                    logger.warning(f"Error fetching mount targets for EFS {fs_id} in {region}: {e}")
                    mount_targets[fs_id] = {"MountTargets": []}

                try:
                    # Get backup policy for this file system
                    backup_policies[fs_id] = await client.describe_backup_policy(FileSystemId=fs_id)
                except Exception as e:
                    logger.warning(f"Error fetching backup policy for EFS {fs_id} in {region}: {e}")
                    backup_policies[fs_id] = {"BackupPolicy": {"Status": "DISABLED"}}

                try:
                    # Get file system policy for this file system
                    file_system_policies[fs_id] = await client.describe_file_system_policy(FileSystemId=fs_id)
                except Exception as e:
                    # Policy might not exist, which is normal
                    if "PolicyNotFound" in str(e) or "does not have a policy" in str(e):
                        file_system_policies[fs_id] = {"Policy": None}
                    else:
                        logger.warning(f"Error fetching file system policy for EFS {fs_id} in {region}: {e}")
                        file_system_policies[fs_id] = {"Policy": None}

        return {
            "region": region,
            "file_systems": file_systems,
            "access_points": access_points,
            "mount_targets": mount_targets,
            "backup_policies": backup_policies,
            "file_system_policies": file_system_policies,
        }
    except Exception as e:
        logger.error(f"Error collecting EFS resources for region {region}: {e}")
        # Return empty structure on error to avoid breaking checks
        return {
            "region": region,
            "file_systems": {"FileSystems": []},
            "access_points": {},
            "mount_targets": {},
            "backup_policies": {},
            "file_system_policies": {},
        }


async def fetch_and_cache_efs_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch EFS resources for a region and cache to JSON file `efs_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    # Filter out invalid regions before processing - EFS is regional service
    import re
    region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")

    if not isinstance(region, str) or not region:
        logger.warning(f"EFS: Skipping invalid region (not string or empty): {region}")
        # Return empty cache file for invalid regions
        cache_path = _region_cache_path(workspace_id, aws_account_id, "invalid")
        with open(cache_path, "w") as fp:
            json.dump({
                "region": region,
                "file_systems": {"FileSystems": []},
                "access_points": {},
                "mount_targets": {},
                "backup_policies": {},
                "file_system_policies": {}
            }, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    if region.lower() == 'global':
        logger.warning(f"EFS: Skipping global region - EFS is regional service: {region}")
        # Return empty cache file for global region
        cache_path = _region_cache_path(workspace_id, aws_account_id, "global")
        with open(cache_path, "w") as fp:
            json.dump({
                "region": region,
                "file_systems": {"FileSystems": []},
                "access_points": {},
                "mount_targets": {},
                "backup_policies": {},
                "file_system_policies": {}
            }, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    if not region_pattern.match(region):
        logger.warning(f"EFS: Skipping invalid region format: {region}")
        # Return empty cache file for invalid format
        cache_path = _region_cache_path(workspace_id, aws_account_id, "invalid-format")
        with open(cache_path, "w") as fp:
            json.dump({
                "region": region,
                "file_systems": {"FileSystems": []},
                "access_points": {},
                "mount_targets": {},
                "backup_policies": {},
                "file_system_policies": {}
            }, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    # logger.info(f"EFS: Processing valid region: {region}")

    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path

    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_efs_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global' - same as EC2/ECS
    import re
    region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")

    logger.info(f"EFS: Original regions received: {regions}")

    valid_regions = []
    for r in regions:
        if not isinstance(r, str) or not r:
            logger.warning(f"EFS: Skipping invalid region (not string or empty): {r}")
            continue
        if r.lower() == 'global':
            logger.warning(f"EFS: Skipping global region - EFS is regional service: {r}")
            continue
        if not region_pattern.match(r):
            logger.warning(f"EFS: Skipping invalid region format: {r}")
            continue
        valid_regions.append(r)

    logger.info(f"EFS: Valid regions after filtering: {valid_regions}")

    if not valid_regions:
        logger.warning("EFS: No valid regions found after filtering")
        return []

    tasks = [fetch_and_cache_efs_region_data(region, session_factory, workspace_id, aws_account_id) for region in valid_regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the EFS-specific caching function.
    This maintains consistency with other service implementations.
    """
    import logging
    logger = logging.getLogger(__name__)

    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id

    logger.info(f"EFS: Starting regional service data fetch for {len(regions)} regions")
    result = await fetch_and_cache_efs_all_regions(regions, session_factory, workspace_id, aws_account_id)
    logger.info(f"EFS: Regional service data fetch completed successfully")
    return result