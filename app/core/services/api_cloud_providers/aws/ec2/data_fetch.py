import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor


CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "ec2")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"ec2_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "ec2")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of EC2-related resources that checks can consume.
    """

    if not isinstance(region, str) or not region or region.lower() == 'global' or '-' not in region:
        raise ValueError(f"Invalid region passed to EC2 fetch: {region!r}")
    async with session.client(AWSServiceNameEnum.EC2.value, region_name=region) as client:
        instances = await client.describe_instances()
        volumes = await client.describe_volumes()
        security_groups = await client.describe_security_groups()
        vpcs = await client.describe_vpcs()
        route_tables = await client.describe_route_tables()
        subnets = await client.describe_subnets()
        internet_gateways = await client.describe_internet_gateways()
        snapshots = await client.describe_snapshots(OwnerIds=["self"])
        transit_gateways = await client.describe_transit_gateways()
        vpn_connections = await client.describe_vpn_connections()
        network_acls = await client.describe_network_acls()
        flow_logs = await client.describe_flow_logs()
        launch_templates = await client.describe_launch_templates()
        client_vpn_endpoints = await client.describe_client_vpn_endpoints()
        vpc_endpoints = await client.describe_vpc_endpoints()

        # Get EBS encryption by default status
        ebs_encryption_by_default = None
        try:
            ebs_encryption_by_default = await client.get_ebs_encryption_by_default()
        except Exception:
            # Some regions might not support this API
            pass

        # Get snapshot attributes for each snapshot
        snapshot_attributes = {}
        for snapshot in snapshots.get("Snapshots", []):
            snapshot_id = snapshot["SnapshotId"]
            try:
                attr_response = await client.describe_snapshot_attribute(
                    SnapshotId=snapshot_id, Attribute="createVolumePermission"
                )
                snapshot_attributes[snapshot_id] = attr_response
            except Exception:
                # Some snapshots may not be accessible or may have been deleted
                snapshot_attributes[snapshot_id] = {"CreateVolumePermissions": []}

        # Get launch template versions for each launch template
        launch_template_versions = {}
        for template in launch_templates.get("LaunchTemplates", []):
            template_id = template["LaunchTemplateId"]
            try:
                # Get the latest version
                latest_version_response = await client.describe_launch_template_versions(
                    LaunchTemplateId=template_id,
                    Versions=["$Latest"]
                )
                # Get all versions for comprehensive checking
                all_versions_response = await client.describe_launch_template_versions(
                    LaunchTemplateId=template_id
                )
                launch_template_versions[template_id] = {
                    "latest": latest_version_response,
                    "all": all_versions_response
                }
            except Exception:
                # Template may not be accessible
                launch_template_versions[template_id] = {
                    "latest": {"LaunchTemplateVersions": []},
                    "all": {"LaunchTemplateVersions": []}
                }

    return {
        "region": region,
        "instances": instances,
        "volumes": volumes,
        "security_groups": security_groups,
        "vpcs": vpcs,
        "route_tables": route_tables,
        "subnets": subnets,
        "internet_gateways": internet_gateways,
        "snapshots": snapshots,
        "transit_gateways": transit_gateways,
        "vpn_connections": vpn_connections,
        "network_acls": network_acls,
        "flow_logs": flow_logs,
        "launch_templates": launch_templates,
        "client_vpn_endpoints": client_vpn_endpoints,
        "vpc_endpoints": vpc_endpoints,
        "ebs_encryption_by_default": ebs_encryption_by_default,
        "snapshot_attributes": snapshot_attributes,
        "launch_template_versions": launch_template_versions,
    }


async def fetch_and_cache_ec2_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch EC2 resources for a region and cache to JSON file `ec2_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_ec2_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []
    tasks = [fetch_and_cache_ec2_region_data(region, session_factory, workspace_id, aws_account_id) for region in regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the EC2-specific caching function.
    This maintains consistency with other service implementations.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id
    
    return await fetch_and_cache_ec2_all_regions(regions, session_factory, workspace_id, aws_account_id)
