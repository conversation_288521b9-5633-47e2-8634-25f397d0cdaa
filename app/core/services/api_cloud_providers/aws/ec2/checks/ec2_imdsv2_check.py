from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "imdsv2": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.IMDSV2.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            for reservation in reservations:
                for instance in reservation.get("Instances", []):
                    instance_id = instance.get("InstanceId")
                    imdsv2_enabled = (
                        (instance.get("MetadataOptions") or {}).get("HttpTokens") == "required"
                    )

                    if findings["imdsv2"]["status"] == ResourceComplianceStatusEnum.PASS.value and not imdsv2_enabled:
                        findings["imdsv2"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["imdsv2"]["details"].append(
                        {
                            "instance_id": instance_id,
                            "region": region,
                            "compliance": True if imdsv2_enabled else False,
                        }
                    )

        return findings


    def remediate(self):
        pass

