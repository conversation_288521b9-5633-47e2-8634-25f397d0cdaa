from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpc_endpoints": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_ENDPOINTS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            vpc_endpoints_data = cached.get("vpc_endpoints") or {}
            existing_endpoints = {vp["ServiceName"]: vp for vp in vpc_endpoints_data.get("VpcEndpoints", [])}

            # Define required endpoints with their types
            required_endpoints = {
                "ec2": {
                    "service_name": f"com.amazonaws.{region}.ec2",
                    "description": "EC2 API endpoint"
                },
                "ecr_api": {
                    "service_name": f"com.amazonaws.{region}.ecr.api",
                    "description": "ECR API endpoint"
                },
                "ecr_dkr": {
                    "service_name": f"com.amazonaws.{region}.ecr.dkr",
                    "description": "ECR Docker Registry endpoint"
                },
                "ssm": {
                    "service_name": f"com.amazonaws.{region}.ssm",
                    "description": "SSM endpoint"
                },
                "ssm_messages": {
                    "service_name": f"com.amazonaws.{region}.ssmmessages",
                    "description": "SSM Messages endpoint"
                },
                "ec2_messages": {
                    "service_name": f"com.amazonaws.{region}.ec2messages",
                    "description": "EC2 Messages endpoint"
                },
                "ssm_contacts": {
                    "service_name": f"com.amazonaws.{region}.ssm-contacts",
                    "description": "SSM Contacts endpoint"
                },
                "ssm_incidents": {
                    "service_name": f"com.amazonaws.{region}.ssm-incidents",
                    "description": "SSM Incidents endpoint"
                }
            }

            overall_compliant = True

            for endpoint_type, config in required_endpoints.items():
                service_name = config["service_name"]
                existing_endpoint = existing_endpoints.get(service_name)

                if existing_endpoint:
                    # Endpoint exists
                    findings["vpc_endpoints"]["details"].append({
                        "endpoint_type": endpoint_type,
                        "service_name": service_name,
                        "vpc_endpoint_id": existing_endpoint["VpcEndpointId"],
                        "region": region,
                        "compliance": True
                    })
                else:
                    # Endpoint doesn't exist
                    findings["vpc_endpoints"]["details"].append({
                        "endpoint_type": endpoint_type,
                        "service_name": service_name,
                        "region": region,
                        "compliance": False
                    })
                    overall_compliant = False

            # Update findings status for this region
            if not overall_compliant and findings["vpc_endpoints"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["vpc_endpoints"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        return findings

    def remediate(self):
        pass
