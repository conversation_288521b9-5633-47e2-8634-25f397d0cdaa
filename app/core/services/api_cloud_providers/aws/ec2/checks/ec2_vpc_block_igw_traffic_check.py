from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpc_block_igw_traffic": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_BLOCK_IGW_TRAFFIC.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}

            # Get Internet Gateways in the region
            igw_data = cached.get("internet_gateways") or {}
            igw_attachments = {}

            for igw in igw_data.get("InternetGateways", []):
                for attachment in igw.get("Attachments", []):
                    vpc_id = attachment.get("VpcId")
                    if vpc_id:
                        igw_attachments[vpc_id] = igw["InternetGatewayId"]

            # Get route tables
            rt_data = cached.get("route_tables") or {}
            vpcs_with_public_routes = set()

            for rt in rt_data.get("RouteTables", []):
                for route in rt.get("Routes", []):
                    if (
                            route.get("DestinationCidrBlock") == "0.0.0.0/0"
                            and route.get("GatewayId", "").startswith("igw-")
                    ):
                        for assoc in rt.get("Associations", []):
                            if assoc.get("Main") or assoc.get("SubnetId"):
                                vpc_id = rt.get("VpcId")
                                if vpc_id:
                                    vpcs_with_public_routes.add(vpc_id)

            # Compare VPCs with IGWs and public routes
            region_findings = []
            for vpc_id, igw_id in igw_attachments.items():
                is_exposed = vpc_id in vpcs_with_public_routes
                region_findings.append({
                    "vpc_id": vpc_id,
                    "internet_gateway_id": igw_id,
                    "region": region,
                    "compliance": not is_exposed
                })

            # Set compliance based on whether any VPCs expose to the internet
            if any(not f["compliance"] for f in region_findings):
                if findings["vpc_block_igw_traffic"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                    findings["vpc_block_igw_traffic"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["vpc_block_igw_traffic"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass