from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ebs_default_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_DEFAULT_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            ebs_encryption_data = cached.get("ebs_encryption_by_default")

            # Determine if encryption is enabled
            encryption_enabled = False
            if ebs_encryption_data:
                encryption_enabled = ebs_encryption_data.get("EbsEncryptionByDefault", False)

            # Update findings for this region
            region_finding = {
                "region": region,
                "compliance": encryption_enabled
            }

            findings["ebs_default_encryption"]["details"].append(region_finding)

            # Update overall status if any region is non-compliant
            if not encryption_enabled and findings["ebs_default_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["ebs_default_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        return findings

    def remediate(self):
        pass
