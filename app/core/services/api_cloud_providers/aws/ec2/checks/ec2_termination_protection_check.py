from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "termination_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.TERMINATION_PROTECTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            for reservation in reservations:
                for instance in reservation.get("Instances", []):
                    instance_id = instance.get("InstanceId")
                    termination_protection = instance.get("DisableApiTermination", False)

                    if (findings["termination_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value and
                            not termination_protection):
                        findings["termination_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["termination_protection"]["details"].append(
                        {
                            "instance_id": instance_id,
                            "region": region,
                            "compliance": True if termination_protection else False,
                        }
                    )

        return findings

    def remediate(self):
        pass
