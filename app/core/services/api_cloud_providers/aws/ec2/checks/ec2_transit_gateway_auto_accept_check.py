from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "transit_gateway_auto_accept": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.TRANSIT_GATEWAY_AUTO_ACCEPT.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            tgw_data = cached.get("transit_gateways") or {}

            region_findings = []
            non_compliant_tgws = []

            for tgw in tgw_data.get("TransitGateways", []):
                tgw_id = tgw["TransitGatewayId"]
                auto_accept = tgw.get("Options", {}).get("AutoAcceptSharedAttachments", "disable")

                # Check if auto-accept is enabled
                is_non_compliant = auto_accept == "enable"
                
                region_findings.append({
                    "transit_gateway_id": tgw_id,
                    "auto_accept": auto_accept,
                    "compliance": not is_non_compliant,
                    "region": region
                })
                
                if is_non_compliant:
                    non_compliant_tgws.append(tgw_id)

            # Determine compliance status for this region
            if non_compliant_tgws and findings["transit_gateway_auto_accept"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["transit_gateway_auto_accept"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["transit_gateway_auto_accept"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
