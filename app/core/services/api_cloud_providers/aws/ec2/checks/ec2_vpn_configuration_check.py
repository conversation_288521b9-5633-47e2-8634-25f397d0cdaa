from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpn_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPN_CONFIGURATION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            vpn_data = cached.get("vpn_connections") or {}

            region_findings = []
            non_compliant_vpns = []

            for vpn in vpn_data.get("VpnConnections", []):
                vpn_id = vpn["VpnConnectionId"]
                logging_options = vpn.get("Options", {}).get("CloudWatchLogOptions", {})
                tunnel_statuses = [tunnel["Status"] for tunnel in vpn.get("VgwTelemetry", [])]

                # Check if logging is enabled
                log_enabled = logging_options.get("LogEnabled", False)
                
                # Check if any tunnel is down
                has_down_tunnel = "DOWN" in tunnel_statuses

                compliance = log_enabled and not has_down_tunnel

                region_findings.append({
                    "vpn_connection_id": vpn_id,
                    "compliance": compliance,
                    "region": region
                })

                if not compliance:
                    non_compliant_vpns.append(vpn_id)

            # Determine compliance status for this region
            if non_compliant_vpns and findings["vpn_configuration"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["vpn_configuration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["vpn_configuration"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
