from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "paravirtual_instances": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.PARAVIRTUAL_INSTANCES.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            region_findings = []
            non_compliant_instances = []

            for reservation in reservations:
                for instance in reservation.get("Instances", []):
                    instance_id = instance["InstanceId"]
                    virtualization_type = instance.get("VirtualizationType", "")
                    instance_type = instance["InstanceType"]

                    # Check if the instance is Paravirtual
                    is_paravirtual = virtualization_type.lower() == "paravirtual"
                    
                    region_findings.append({
                        "instance_id": instance_id,
                        "virtualization_type": virtualization_type,
                        "instance_type": instance_type,
                        "compliance": not is_paravirtual,
                        "region": region
                    })
                    
                    if is_paravirtual:
                        non_compliant_instances.append(instance_id)

            # Determine compliance status for this region
            if non_compliant_instances and findings["paravirtual_instances"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["paravirtual_instances"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["paravirtual_instances"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
