from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "nacl_unrestricted_ssh_rdp": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.NACL_UNRESTRICTED_SSH_RDP.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            nacl_data = cached.get("network_acls") or {}

            region_findings = []
            non_compliant_nacls = []

            for nacl in nacl_data.get("NetworkAcls", []):
                nacl_id = nacl["NetworkAclId"]

                for entry in nacl.get("Entries", []):
                    # Check if the rule allows traffic (not DENY)
                    if entry["RuleAction"] == "allow":
                        # Check if the rule allows ingress (inbound traffic)
                        if entry["Egress"] is False:
                            # Check if the rule allows traffic from anywhere (0.0.0.0/0)
                            if entry["CidrBlock"] == "0.0.0.0/0":
                                # Check if the rule applies to port 22 (SSH) or 3389 (RDP)
                                port_range = entry.get("PortRange", {})
                                port_from = port_range.get("From")
                                port_to = port_range.get("To")
                                
                                if port_from in [22, 3389] or port_to in [22, 3389]:
                                    region_findings.append({
                                        "nacl_id": nacl_id,
                                        "rule_number": entry["RuleNumber"],
                                        "protocol": entry["Protocol"],
                                        "port_from": port_from,
                                        "port_to": port_to,
                                        "cidr_block": entry["CidrBlock"],
                                        "compliance": False,
                                        "region": region,
                                    })
                                    non_compliant_nacls.append(nacl_id)

            # Determine compliance status for this region
            if non_compliant_nacls and findings["nacl_unrestricted_ssh_rdp"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["nacl_unrestricted_ssh_rdp"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["nacl_unrestricted_ssh_rdp"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
