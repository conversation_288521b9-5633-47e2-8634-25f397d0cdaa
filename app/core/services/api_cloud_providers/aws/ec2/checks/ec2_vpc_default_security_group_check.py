from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpc_default_security_group": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_DEFAULT_SECURITY_GROUP.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            vpcs_data = cached.get("vpcs") or {}
            security_groups_data = cached.get("security_groups") or {}

            # Get all VPCs
            vpc_ids = [vpc["VpcId"] for vpc in vpcs_data.get("Vpcs", [])]

            region_findings = []
            non_compliant_sgs = []

            for vpc_id in vpc_ids:
                # Find the default security group for the VPC
                default_sgs = [
                    sg for sg in security_groups_data.get("SecurityGroups", [])
                    if sg.get("GroupName") == "default" and sg.get("VpcId") == vpc_id
                ]

                for sg in default_sgs:
                    sg_id = sg["GroupId"]
                    inbound_rules = sg.get("IpPermissions", [])
                    outbound_rules = sg.get("IpPermissionsEgress", [])

                    # Check if the default SG has any inbound or outbound rules
                    has_rules = bool(inbound_rules or outbound_rules)
                    
                    region_findings.append({
                        "vpc_id": vpc_id,
                        "security_group_id": sg_id,
                        "inbound_rules": len(inbound_rules),
                        "outbound_rules": len(outbound_rules),
                        "compliance": not has_rules,
                        "region": region
                    })
                    
                    if has_rules:
                        non_compliant_sgs.append(sg_id)

            # Determine compliance status for this region
            if non_compliant_sgs and findings["vpc_default_security_group"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["vpc_default_security_group"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["vpc_default_security_group"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
