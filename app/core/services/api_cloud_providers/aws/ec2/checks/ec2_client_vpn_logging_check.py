from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ec2_client_vpn_logging": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_CLIENT_VPN_LOGGING.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            client_vpn_data = cached.get("client_vpn_endpoints") or {}

            region_findings = []
            non_compliant_vpns = []

            for vpn in client_vpn_data.get("ClientVpnEndpoints", []):
                vpn_id = vpn["ClientVpnEndpointId"]
                cloudwatch_log_group = vpn.get("ConnectionLogOptions", {}).get("CloudwatchLogGroup", None)
                logging_enabled = bool(cloudwatch_log_group)

                region_findings.append({
                    "vpn_id": vpn_id,
                    "region": region,
                    "cloudwatch_log_group": cloudwatch_log_group,
                    "compliance": logging_enabled
                })

                # If logging is not enabled, flag as non-compliant
                if not logging_enabled:
                    non_compliant_vpns.append(vpn_id)

            # Determine compliance status for this region
            if non_compliant_vpns and findings["ec2_client_vpn_logging"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["ec2_client_vpn_logging"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["ec2_client_vpn_logging"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
