import datetime
from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum, STOPPED_EC2_INSTANCE_CLEANUP_TIME
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "stopped_instances_cleanup": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.STOPPED_INSTANCES_CLEANUP.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        current_time = datetime.datetime.utcnow()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            region_findings = []
            non_compliant_instances = []

            for reservation in reservations:
                for instance in reservation.get("Instances", []):
                    instance_id = instance["InstanceId"]
                    state = instance["State"]["Name"]
                    launch_time = instance["LaunchTime"]
                    instance_type = instance["InstanceType"]

                    # If the instance is stopped, calculate how long it has been stopped
                    if state == "stopped":
                        # Convert launch_time string to datetime if it's a string
                        if isinstance(launch_time, str):
                            launch_time = datetime.datetime.fromisoformat(launch_time.replace('Z', '+00:00'))
                        
                        stopped_days = (current_time - launch_time.replace(tzinfo=None)).days

                        is_non_compliant = stopped_days > STOPPED_EC2_INSTANCE_CLEANUP_TIME
                        
                        region_findings.append({
                            "instance_id": instance_id,
                            "stopped_days": stopped_days,
                            "instance_type": instance_type,
                            "state": state,
                            "compliance": not is_non_compliant,
                            "region": region
                        })
                        
                        if is_non_compliant:
                            non_compliant_instances.append(instance_id)
                    else:
                        region_findings.append({
                            "instance_id": instance_id,
                            "stopped_days": 0,
                            "instance_type": instance_type,
                            "state": state,
                            "compliance": True,
                            "region": region
                        })

            # Determine compliance status for this region
            if non_compliant_instances and findings["stopped_instances_cleanup"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["stopped_instances_cleanup"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["stopped_instances_cleanup"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
