from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ec2_multiple_enis": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_MULTIPLE_ENIS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            region_findings = []
            non_compliant_instances = []

            # Flatten instances from all reservations
            instances = []
            for reservation in reservations:
                instances.extend(reservation.get("Instances", []))

            for instance in instances:
                instance_id = instance["InstanceId"]
                network_interfaces = instance.get("NetworkInterfaces", [])

                # If an instance has more than one ENI, mark as non-compliant
                has_multiple_enis = len(network_interfaces) > 1
                
                region_findings.append({
                    "instance_id": instance_id,
                    "region": region,
                    "network_interfaces_count": len(network_interfaces),
                    "compliance": not has_multiple_enis
                })
                
                if has_multiple_enis:
                    non_compliant_instances.append(instance_id)

            # Determine compliance status for this region
            if non_compliant_instances and findings["ec2_multiple_enis"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["ec2_multiple_enis"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["ec2_multiple_enis"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
