from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "subnet_launch_template_public_ip": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.SUBNET_LAUNCH_TEMPLATE_PUBLIC_IP.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            
            region_findings = []
            non_compliant_resources = []

            # Check subnets
            subnets_data = cached.get("subnets") or {}
            for subnet in subnets_data.get("Subnets", []):
                subnet_id = subnet["SubnetId"]
                vpc_id = subnet["VpcId"]
                auto_assign_public_ip = subnet["MapPublicIpOnLaunch"]

                region_findings.append({
                    "resource_type": "subnet",
                    "resource_id": subnet_id,
                    "compliance": not auto_assign_public_ip,
                    "region": region
                })

                if auto_assign_public_ip:
                    non_compliant_resources.append(subnet_id)

            # Check launch templates
            launch_templates_data = cached.get("launch_templates") or {}
            launch_template_versions = cached.get("launch_template_versions") or {}

            for template in launch_templates_data.get("LaunchTemplates", []):
                template_id = template["LaunchTemplateId"]
                template_name = template["LaunchTemplateName"]

                # Check if launch template assigns public IP using cached template versions
                template_versions = launch_template_versions.get(template_id, {})
                all_versions = template_versions.get("all", {}).get("LaunchTemplateVersions", [])

                assigns_public_ip = False
                for version in all_versions:
                    network_interfaces = version.get("LaunchTemplateData", {}).get("NetworkInterfaces", [])
                    if any(interface.get("AssociatePublicIpAddress", False) for interface in network_interfaces):
                        assigns_public_ip = True
                        break

                region_findings.append({
                    "resource_type": "launch_template",
                    "resource_id": template_id,
                    "vpc_id": None,
                    "assigns_public_ip": assigns_public_ip,
                    "compliance": not assigns_public_ip,
                    "region": region
                })

                if assigns_public_ip:
                    non_compliant_resources.append(template_id)

            # Determine compliance status for this region
            if non_compliant_resources and findings["subnet_launch_template_public_ip"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["subnet_launch_template_public_ip"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["subnet_launch_template_public_ip"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
