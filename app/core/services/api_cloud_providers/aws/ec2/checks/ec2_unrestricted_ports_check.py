from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "unrestricted_ports": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.UNRESTRICTED_PORTS.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            security_groups_data = cached.get("security_groups") or {}

            # Define high-risk ports and authorized ports
            high_risk_ports = [22, 3389, 3306, 5432, 27017, 6379, 5900, 9200]
            authorized_ports = {80, 443, 123, 53}

            region_findings = []
            vulnerable_sgs = []

            for sg in security_groups_data.get("SecurityGroups", []):
                sg_id = sg["GroupId"]
                sg_name = sg["GroupName"]
                vpc_id = sg["VpcId"]
                
                # Track violations for this security group
                high_risk_violations = []
                unauthorized_violations = []
                has_violations = False

                # Check inbound rules for unrestricted access
                for rule in sg.get("IpPermissions", []):
                    port = rule.get("FromPort")

                    if port:
                        # Check for unrestricted access (0.0.0.0/0 or ::/0)
                        is_unrestricted = (
                            any(ip_range.get("CidrIp") == "0.0.0.0/0" for ip_range in rule.get("IpRanges", [])) or
                            any(ipv6_range.get("CidrIpv6") == "::/0" for ipv6_range in rule.get("Ipv6Ranges", []))
                        )
                        
                        if is_unrestricted:
                            is_high_risk = port in high_risk_ports
                            is_unauthorized = port not in authorized_ports
                            
                            if is_high_risk:
                                high_risk_violations.append(port)
                                has_violations = True
                            elif is_unauthorized:
                                unauthorized_violations.append(port)
                                has_violations = True

                # Add finding for this security group
                region_findings.append({
                    "security_group_id": sg_id,
                    "security_group_name": sg_name,
                    "vpc_id": vpc_id,
                    "compliance": not has_violations,
                    "region": region
                })

                if has_violations:
                    vulnerable_sgs.append(sg_id)

            # Determine compliance status for this region
            if vulnerable_sgs and findings["unrestricted_ports"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["unrestricted_ports"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["unrestricted_ports"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
