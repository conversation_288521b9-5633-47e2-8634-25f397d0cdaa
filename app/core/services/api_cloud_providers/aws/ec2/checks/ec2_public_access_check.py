from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            reservations = (cached.get("instances") or {}).get("Reservations", [])

            for reservation in reservations:
                for instance in reservation.get("Instances", []):
                    instance_id = instance.get("InstanceId")
                    has_public_access = instance.get("PublicIpAddress") is not None

                    if findings["public_access"]["status"] == ResourceComplianceStatusEnum.PASS.value and has_public_access:
                        findings["public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["public_access"]["details"].append(
                        {
                            "instance_id": instance_id,
                            "region": region,
                            "compliance": False if has_public_access else True,
                        }
                    )

        return findings

    def remediate(self):
        pass
