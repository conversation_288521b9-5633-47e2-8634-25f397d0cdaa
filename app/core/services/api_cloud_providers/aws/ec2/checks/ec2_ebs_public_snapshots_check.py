from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ebs_public_snapshots": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_PUBLIC_SNAPSHOTS.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            snapshots_data = cached.get("snapshots") or {}
            snapshot_attributes = cached.get("snapshot_attributes") or {}

            region_findings = []
            public_snapshots = []

            for snapshot in snapshots_data.get("Snapshots", []):
                snapshot_id = snapshot["SnapshotId"]

                # Check if the snapshot is publicly accessible using cached attributes
                snapshot_attr = snapshot_attributes.get(snapshot_id, {})
                public_access = any(
                    perm.get("Group") == "all"
                    for perm in snapshot_attr.get("CreateVolumePermissions", [])
                )

                # Update findings
                region_findings.append({
                    "snapshot_id": snapshot_id,
                    "publicly_restorable": public_access,
                    "region": region,
                    "compliance": not public_access
                })

                if public_access:
                    public_snapshots.append(snapshot_id)

            # Determine compliance status for this region
            if public_snapshots and findings["ebs_public_snapshots"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["ebs_public_snapshots"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["ebs_public_snapshots"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass
