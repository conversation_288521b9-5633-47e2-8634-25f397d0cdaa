import asyncio
import json
import os
import logging
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

# Set up logging
logger = logging.getLogger(__name__)

CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "ecs")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"ecs_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "ecs")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


def _create_resource_key(cluster_arn: str, resource_arn: str) -> str:
    """
    Create a safe composite key for cluster-scoped resources.
    Uses a separator that's unlikely to appear in ARNs.
    """
    return f"{cluster_arn}|--|{resource_arn}"


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of ECS-related resources that checks can consume.
    Following EC2's simple pattern to avoid deadlocks - FAST execution only.
    """
    # Region validation already done in fetch_and_cache_ecs_region_data
    # Keep this function simple and fast like EC2

    try:
        async with session.client(AWSServiceNameEnum.ECS.value, region_name=region) as client:
            # Only basic API calls like EC2 - no complex nested operations to avoid deadlocks
            clusters = await client.list_clusters()
            task_definitions = await client.list_task_definitions()

            # Minimal cluster details - keep it simple like EC2
            cluster_details = {}
            cluster_arns = clusters.get("clusterArns", [])
            if cluster_arns and len(cluster_arns) <= 5:  # Limit to avoid long operations
                try:
                    clusters_desc = await client.describe_clusters(clusters=cluster_arns[:5])
                    for cluster in clusters_desc.get("clusters", []):
                        cluster_details[cluster['clusterArn']] = {
                            'clusterName': cluster.get('clusterName'),
                            'status': cluster.get('status'),
                            'runningTasksCount': cluster.get('runningTasksCount', 0),
                            'activeServicesCount': cluster.get('activeServicesCount', 0)
                        }
                except Exception as e:
                    logger.warning(f"Error fetching cluster details for {region}: {e}")
                    cluster_details = {}

        return {
            "region": region,
            "clusters": clusters,
            "task_definitions": task_definitions,
            "cluster_details": cluster_details,
        }
    except Exception as e:
        logger.error(f"Error collecting ECS resources for region {region}: {e}")
        # Return empty structure on error to avoid breaking checks
        return {
            "region": region,
            "clusters": {"clusterArns": []},
            "task_definitions": {"taskDefinitionArns": []},
            "cluster_details": {},
        }





async def fetch_and_cache_ecs_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch ECS resources for a region and cache to JSON file `ecs_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    # Filter out invalid regions before processing - ECS is regional service
    import re
    region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")

    if not isinstance(region, str) or not region:
        logger.warning(f"ECS: Skipping invalid region (not string or empty): {region}")
        # Return empty cache file for invalid regions
        cache_path = _region_cache_path(workspace_id, aws_account_id, "invalid")
        with open(cache_path, "w") as fp:
            json.dump({"region": region, "clusters": {"clusterArns": []}, "task_definitions": {"taskDefinitionArns": []}, "cluster_details": {}}, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    if region.lower() == 'global':
        logger.warning(f"ECS: Skipping global region - ECS is regional service: {region}")
        # Return empty cache file for global region
        cache_path = _region_cache_path(workspace_id, aws_account_id, "global")
        with open(cache_path, "w") as fp:
            json.dump({"region": region, "clusters": {"clusterArns": []}, "task_definitions": {"taskDefinitionArns": []}, "cluster_details": {}}, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    if not region_pattern.match(region):
        logger.warning(f"ECS: Skipping invalid region format: {region}")
        # Return empty cache file for invalid format
        cache_path = _region_cache_path(workspace_id, aws_account_id, "invalid-format")
        with open(cache_path, "w") as fp:
            json.dump({"region": region, "clusters": {"clusterArns": []}, "task_definitions": {"taskDefinitionArns": []}, "cluster_details": {}}, fp, cls=AWSJSONEncoder, indent=2)
        return cache_path

    # logger.info(f"ECS: Processing valid region: {region}")

    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        # logger.info(f"Using cached ECS data for region {region}")
        return cache_path

    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    # logger.info(f"Cached ECS data for region {region} to {cache_path}")
    return cache_path


async def fetch_and_cache_ecs_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global' - same as EC2
    import re
    region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")

    logger.info(f"ECS: Original regions received: {regions}")

    valid_regions = []
    for r in regions:
        if not isinstance(r, str) or not r:
            logger.warning(f"ECS: Skipping invalid region (not string or empty): {r}")
            continue
        if r.lower() == 'global':
            logger.warning(f"ECS: Skipping global region - ECS is regional service: {r}")
            continue
        if not region_pattern.match(r):
            logger.warning(f"ECS: Skipping invalid region format: {r}")
            continue
        valid_regions.append(r)

    logger.info(f"ECS: Valid regions after filtering: {valid_regions}")

    if not valid_regions:
        logger.warning("ECS: No valid regions found after filtering")
        return []

    tasks = [fetch_and_cache_ecs_region_data(region, session_factory, workspace_id, aws_account_id) for region in valid_regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the ECS-specific caching function.
    This maintains consistency with other service implementations.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id
    
    return await fetch_and_cache_ecs_all_regions(regions, session_factory, workspace_id, aws_account_id)