from fastapi.responses import Response
from fastapi import HTTPException, status
from app import router
from app.common import APP_READINESS_API, APP_LIVENESS_API, RABBITMQ_CONSUMER_HEALTH_TEST_API, HttpStatusCodeEnum
from app.core.services.queue_service.rabbitmq_client import restart_dead_consumers
import logging

logger = logging.getLogger(__name__)

__all__ = ["service_status", "queue_service_health", "connection_health"]


@router.get(APP_READINESS_API)
async def service_status():
    """Enhanced readiness check with connection monitoring"""
    try:
        from app.core.models.mysql.mysql_mgr import get_connection_stats
        stats = get_connection_stats()

        # Check if connection utilization is too high
        if stats['utilization_percent'] > 90:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "status": "not_ready",
                    "reason": "high_connection_utilization",
                    "utilization_percent": stats['utilization_percent'],
                    "connections": f"{stats['total_connections']}/{stats['aurora_connection_limit']}"
                }
            )

        return Response(status_code=HttpStatusCodeEnum.NO_CONTENT.value)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "not_ready", "error": str(e)}
        )


@router.get(APP_LIVENESS_API)
async def liveness_check():
    """Basic liveness check"""
    return Response(status_code=HttpStatusCodeEnum.NO_CONTENT.value)


@router.get(RABBITMQ_CONSUMER_HEALTH_TEST_API)
async def queue_service_health():
    """This method is resolved the rabbitmq consumer auto discard issue"""
    await restart_dead_consumers()
    return Response(status_code=HttpStatusCodeEnum.NO_CONTENT.value)


@router.get("/connection-health")
async def connection_health():
    """Get detailed connection health information"""
    try:
        from app.core.models.mysql.mysql_mgr import get_connection_stats
        from app.core.models.mysql.connection_monitor import get_connection_monitor

        stats = get_connection_stats()
        monitor = get_connection_monitor(max_connections=60)
        summary = monitor.get_stats_summary(minutes=10)

        # Determine health status
        utilization = stats['utilization_percent']
        if utilization >= 95:
            health_status = "critical"
        elif utilization >= 80:
            health_status = "warning"
        elif utilization >= 60:
            health_status = "caution"
        else:
            health_status = "healthy"

        return {
            "status": health_status,
            "current_stats": stats,
            "summary_10min": summary,
            "recommendations": _get_recommendations(stats)
        }

    except Exception as e:
        logger.error(f"Connection health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": str(e)}
        )


def _get_recommendations(stats):
    """Get recommendations based on current connection usage"""
    utilization = stats['utilization_percent']
    recommendations = []

    if utilization >= 90:
        recommendations.append("URGENT: Connection usage is critically high. Consider reducing Celery worker concurrency.")
        recommendations.append("Monitor for connection leaks and restart workers if necessary.")
    elif utilization >= 70:
        recommendations.append("WARNING: Connection usage is high. Monitor closely.")
        recommendations.append("Consider optimizing database queries or reducing concurrent tasks.")
    elif utilization >= 50:
        recommendations.append("Connection usage is moderate. Continue monitoring.")
    else:
        recommendations.append("Connection usage is healthy.")

    if stats['total_pools'] > 4:
        recommendations.append(f"High number of connection pools ({stats['total_pools']}). Consider pool cleanup.")

    return recommendations
