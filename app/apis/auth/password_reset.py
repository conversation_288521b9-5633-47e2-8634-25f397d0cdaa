"""
Password Reset API Endpoints

This module contains API endpoints for password reset functionality.
Follows the same redirect pattern as email verification.
"""

from fastapi import Request, Response
from fastapi.responses import RedirectResponse
from app import router, app
from app.common import (
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
    OTPResendCooldownException, DefaultResponseSchema, CloudAuditException,
    FORGOT_PASSWORD_API, RESET_PASSWORD_API
)
from app.core.services.auth.password_reset import (
    ForgotPasswordService, ResetPasswordService, ValidateResetTokenService
)

__all__ = ['forgot_password', 'reset_password_redirect', 'reset_password', 'validate_reset_token']


@router.post(FORGOT_PASSWORD_API, response_model=DefaultResponseSchema)
async def forgot_password(request: Request, response: Response, payload: dict):
    """
    Handle forgot password requests
    
    This endpoint accepts an email address and sends a password reset link
    if the email exists in the system. Always returns success to prevent
    email enumeration attacks.
    
    Expected payload: {"email": "<EMAIL>"}
    """
    try:
        # Extract email from payload
        email = payload.get('email')
        if not email:
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Email address is required"
            )
        
        # Create a message object with the email
        message = type('obj', (object,), {'email': email})()
        
        service = ForgotPasswordService(message)
        result = await service.process()
        
        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "expires_in_minutes": result["expires_in_minutes"]
            }
        )
    
    except OTPResendCooldownException as e:
        response.status_code = 429
        return DefaultResponseSchema(
            ok=False,
            status_code=429,
            message=str(e),
            data={"error_type": "rate_limited"}
        )
    
    except Exception as e:
        # Log the actual error for debugging
        import logging
        logging.error(f"Unexpected error in forgot_password: {str(e)}", exc_info=True)

        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to process password reset request. Please try again.",
            data={"error_type": "internal_error"}
        )


@router.get("/reset-password")
async def reset_password_redirect(request: Request, response: Response, token: str) -> RedirectResponse:
    """
    Handle password reset link clicks from email
    Redirects users to the frontend with appropriate query parameters.

    This endpoint is called when users click the reset password link in their email.
    It validates the token and redirects to the frontend reset password form.

    Args:
        token: Password reset token from the email link

    Returns:
        RedirectResponse: Redirect to frontend with status parameters
    """
    frontend_url = app.config.FRONTEND_URL

    # Debug logging
    print(f"🔍 GET /reset-password called")
    print(f"🎫 Raw token received: '{token}'")
    print(f"🎫 Token length: {len(token)}")
    print(f"🎫 Token type: {type(token)}")

    try:
        # Create a message object with the token
        message = type('obj', (object,), {'token': token})()

        print(f"🔍 About to validate token with ValidateResetTokenService")
        service = ValidateResetTokenService(message)
        result = await service.process()
        print(f"✅ Token validation successful: {result}")

        # Redirect to reset password form with valid token
        redirect_url = f"{frontend_url}/reset-password?token={token}"
        print(f"🔄 Redirecting to: {redirect_url}")
        print(f"🌐 Frontend URL configured as: {frontend_url}")

        return RedirectResponse(
            url=redirect_url,
            status_code=302
        )
    
    except OTPExpiredException as e:
        # Token has expired - redirect to forgot password with error
        print(f"❌ Token expired: {str(e)}")
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=expired",
            status_code=302
        )

    except OTPNotFoundException as e:
        # Invalid token - redirect to forgot password with error
        print(f"❌ Token not found: {str(e)}")
        print(f"🔍 This means the token '{token}' was not found in the database")
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=invalid",
            status_code=302
        )

    except OTPAlreadyVerifiedException as e:
        # Token already used - redirect to login with info
        print(f"❌ Token already used: {str(e)}")
        return RedirectResponse(
            url=f"{frontend_url}/login?info=password_already_reset",
            status_code=302
        )

    except Exception as e:
        # Generic error - redirect to forgot password with error
        print(f"❌ Unexpected error in GET /reset-password: {str(e)}")
        print(f"🔍 Token was: '{token}'")
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=validation_failed",
            status_code=302
        )


@router.post("/validate-reset-token", response_model=DefaultResponseSchema)
async def validate_reset_token(request: Request, response: Response, payload: dict):
    """
    Validate password reset token without consuming it

    This endpoint validates a reset token and returns user information
    if the token is valid. Used by frontend to check token validity
    before showing the reset form.

    Expected payload: {"token": "reset_token_here"}
    """
    try:
        # Extract token from payload
        token = payload.get('token')
        if not token:
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Token is required"
            )

        # Debug logging
        print(f"🔍 POST /validate-reset-token called")
        print(f"🎫 Token received: '{token}'")
        print(f"🎫 Token length: {len(token)}")

        # Create a message object with the token
        message = type('obj', (object,), {'token': token})()

        service = ValidateResetTokenService(message)
        result = await service.process()

        print(f"✅ Token validation successful: {result}")

        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message="Token is valid",
            data={
                "email": result.get('email'),
                "expires_at": str(result.get('expires_at'))
            }
        )

    except OTPExpiredException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Reset token has expired. Please request a new password reset.",
            data={"error_type": "token_expired"}
        )

    except OTPNotFoundException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Invalid reset token. Please request a new password reset.",
            data={"error_type": "invalid_token"}
        )

    except OTPAlreadyVerifiedException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="This reset token has already been used. Please request a new password reset.",
            data={"error_type": "token_already_used"}
        )

    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to validate reset token. Please try again.",
            data={"error_type": "internal_error"}
        )


@router.post(RESET_PASSWORD_API, response_model=DefaultResponseSchema)
async def reset_password(request: Request, response: Response, payload: dict):
    """
    Handle password reset with new password
    
    This endpoint accepts a reset token, new password, and confirmation
    to complete the password reset process.
    
    Expected payload: {
        "token": "reset_token_here",
        "new_password": "NewSecurePassword123!",
        "confirm_password": "NewSecurePassword123!"
    }
    """
    try:
        # Extract required fields from payload
        token = payload.get('token')
        new_password = payload.get('new_password')
        confirm_password = payload.get('confirm_password')

        # Debug logging
        print(f"🔍 POST /reset-password called")
        print(f"🎫 Token received: '{token}'")
        print(f"🎫 Token length: {len(token) if token else 'None'}")
        print(f"🎫 Token type: {type(token)}")

        if not all([token, new_password, confirm_password]):
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Token, new password, and confirmation are required"
            )
        
        # Create a message object with the data
        message = type('obj', (object,), {
            'token': token,
            'new_password': new_password,
            'confirm_password': confirm_password
        })()
        
        service = ResetPasswordService(message)
        result = await service.process()
        
        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "success": result["success"]
            }
        )
    
    except OTPExpiredException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Reset token has expired. Please request a new password reset.",
            data={"error_type": "token_expired"}
        )
    
    except OTPNotFoundException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Invalid reset token. Please request a new password reset.",
            data={"error_type": "invalid_token"}
        )
    
    except OTPAlreadyVerifiedException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="This reset token has already been used. Please request a new password reset.",
            data={"error_type": "token_used"}
        )
    
    except CloudAuditException as e:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message=str(e),
            data={"error_type": "validation_error"}
        )
    
    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to reset password. Please try again.",
            data={"error_type": "internal_error"}
        )


@router.get("/debug-reset-token")
async def debug_reset_token(request: Request, token: str):
    """
    Debug endpoint to check token validation without redirect
    Helps identify if the issue is with token validation or frontend handling
    """
    try:
        # Create a message object with the token
        message = type('obj', (object,), {'token': token})()

        service = ValidateResetTokenService(message)
        result = await service.process()

        return {
            "ok": True,
            "status_code": 200,
            "message": "Token validation successful",
            "data": {
                "token_valid": True,
                "token_received": token,
                "token_length": len(token),
                "email": result.get('email'),
                "expires_at": str(result.get('expires_at')),
                "frontend_url": app.config.FRONTEND_URL,
                "expected_redirect": f"{app.config.FRONTEND_URL}/reset-password?token={token}"
            }
        }

    except OTPExpiredException:
        return {
            "ok": False,
            "status_code": 400,
            "message": "Token has expired",
            "data": {
                "token_valid": False,
                "error_type": "expired",
                "token_received": token,
                "token_length": len(token)
            }
        }

    except OTPNotFoundException:
        return {
            "ok": False,
            "status_code": 400,
            "message": "Token not found in database",
            "data": {
                "token_valid": False,
                "error_type": "not_found",
                "token_received": token,
                "token_length": len(token)
            }
        }

    except OTPAlreadyVerifiedException:
        return {
            "ok": False,
            "status_code": 400,
            "message": "Token has already been used",
            "data": {
                "token_valid": False,
                "error_type": "already_used",
                "token_received": token,
                "token_length": len(token)
            }
        }

    except Exception as e:
        return {
            "ok": False,
            "status_code": 500,
            "message": f"Validation error: {str(e)}",
            "data": {
                "token_valid": False,
                "error_type": "validation_error",
                "token_received": token,
                "token_length": len(token)
            }
        }
