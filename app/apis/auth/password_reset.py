"""
Password Reset API Endpoints

This module contains API endpoints for password reset functionality.
Follows the same redirect pattern as email verification.
"""

from fastapi import Request, Response
from fastapi.responses import RedirectResponse
from app import router, app
from app.common import (
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
    OTPResendCooldownException, DefaultResponseSchema, CloudAuditException
)
from app.core.services.auth.password_reset import (
    ForgotPasswordService, ResetPasswordService, ValidateResetTokenService
)

__all__ = ['forgot_password', 'reset_password_redirect', 'reset_password']


@router.post("/forgot-password", response_model=DefaultResponseSchema)
async def forgot_password(request: Request, response: Response, payload: dict):
    """
    Handle forgot password requests
    
    This endpoint accepts an email address and sends a password reset link
    if the email exists in the system. Always returns success to prevent
    email enumeration attacks.
    
    Expected payload: {"email": "<EMAIL>"}
    """
    try:
        # Extract email from payload
        email = payload.get('email')
        if not email:
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Email address is required"
            )
        
        # Create a message object with the email
        message = type('obj', (object,), {'email': email})()
        
        service = ForgotPasswordService(message)
        result = await service.process()
        
        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "expires_in_minutes": result["expires_in_minutes"]
            }
        )
    
    except OTPResendCooldownException as e:
        response.status_code = 429
        return DefaultResponseSchema(
            ok=False,
            status_code=429,
            message=str(e),
            data={"error_type": "rate_limited"}
        )
    
    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to process password reset request. Please try again.",
            data={"error_type": "internal_error"}
        )


@router.get("/reset-password")
async def reset_password_redirect(request: Request, response: Response, token: str) -> RedirectResponse:
    """
    Handle password reset link clicks from email
    Redirects users to the frontend with appropriate query parameters.
    
    This endpoint is called when users click the reset password link in their email.
    It validates the token and redirects to the frontend reset password form.
    
    Args:
        token: Password reset token from the email link
    
    Returns:
        RedirectResponse: Redirect to frontend with status parameters
    """
    frontend_url = app.config.FRONTEND_URL
    
    try:
        # Create a message object with the token
        message = type('obj', (object,), {'token': token})()
        
        service = ValidateResetTokenService(message)
        result = await service.process()
        
        # Redirect to reset password form with valid token
        return RedirectResponse(
            url=f"{frontend_url}/reset-password?token={token}",
            status_code=302
        )
    
    except OTPExpiredException:
        # Token has expired - redirect to forgot password with error
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=expired",
            status_code=302
        )
    
    except OTPNotFoundException:
        # Invalid token - redirect to forgot password with error
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=invalid",
            status_code=302
        )
    
    except OTPAlreadyVerifiedException:
        # Token already used - redirect to login with info
        return RedirectResponse(
            url=f"{frontend_url}/login?info=password_already_reset",
            status_code=302
        )
    
    except Exception as e:
        # Generic error - redirect to forgot password with error
        return RedirectResponse(
            url=f"{frontend_url}/forgot-password?error=validation_failed",
            status_code=302
        )


@router.post("/reset-password", response_model=DefaultResponseSchema)
async def reset_password(request: Request, response: Response, payload: dict):
    """
    Handle password reset with new password
    
    This endpoint accepts a reset token, new password, and confirmation
    to complete the password reset process.
    
    Expected payload: {
        "token": "reset_token_here",
        "new_password": "NewSecurePassword123!",
        "confirm_password": "NewSecurePassword123!"
    }
    """
    try:
        # Extract required fields from payload
        token = payload.get('token')
        new_password = payload.get('new_password')
        confirm_password = payload.get('confirm_password')
        
        if not all([token, new_password, confirm_password]):
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Token, new password, and confirmation are required"
            )
        
        # Create a message object with the data
        message = type('obj', (object,), {
            'token': token,
            'new_password': new_password,
            'confirm_password': confirm_password
        })()
        
        service = ResetPasswordService(message)
        result = await service.process()
        
        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "success": result["success"]
            }
        )
    
    except OTPExpiredException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Reset token has expired. Please request a new password reset.",
            data={"error_type": "token_expired"}
        )
    
    except OTPNotFoundException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Invalid reset token. Please request a new password reset.",
            data={"error_type": "invalid_token"}
        )
    
    except OTPAlreadyVerifiedException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="This reset token has already been used. Please request a new password reset.",
            data={"error_type": "token_used"}
        )
    
    except CloudAuditException as e:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message=str(e),
            data={"error_type": "validation_error"}
        )
    
    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to reset password. Please try again.",
            data={"error_type": "internal_error"}
        )
